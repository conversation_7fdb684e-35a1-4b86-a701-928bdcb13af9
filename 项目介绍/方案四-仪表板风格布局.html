<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>方案四：仪表板风格布局</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background: #f5f6fa;
            color: #2c3e50;
        }

        .dashboard-layout {
            min-height: 100vh;
            padding: 20px;
        }

        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 40px;
            border-radius: 15px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 700;
            margin: 0;
        }

        .header-actions button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .header-actions button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            grid-template-rows: auto auto;
            gap: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .grid-item {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .grid-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .card-header {
            background: #f8f9fa;
            padding: 20px 24px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
        }

        .card-header i {
            font-size: 20px;
            margin-right: 12px;
            color: #667eea;
        }

        .card-header span {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
        }

        .card-content {
            padding: 24px;
        }

        /* 项目信息卡片 */
        .info-card {
            grid-column: 1;
            grid-row: 1;
        }

        .info-card .card-content p {
            line-height: 1.8;
            color: #666;
            font-size: 15px;
            text-align: justify;
        }

        /* 统计卡片 */
        .stats-card {
            grid-column: 2;
            grid-row: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .stats-card .card-header {
            background: rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stats-card .card-header i,
        .stats-card .card-header span {
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 24px;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 8px;
            color: white;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 500;
        }

        /* 图片展示区域 */
        .gallery-section {
            grid-column: 1 / -1;
            grid-row: 2;
        }

        .section-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e9ecef;
            background: #f8f9fa;
        }

        .section-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
        }

        .section-header h3 i {
            margin-right: 10px;
            color: #667eea;
        }

        .dashboard-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 24px;
        }

        .dashboard-image {
            position: relative;
            height: 180px;
            border-radius: 10px;
            overflow: hidden;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .dashboard-image:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .image-badge {
            position: absolute;
            top: 12px;
            right: 12px;
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 700;
        }

        /* 进度条 */
        .progress-section {
            margin-top: 20px;
        }

        .progress-item {
            margin-bottom: 15px;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .progress-bar {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto;
            }
            
            .info-card {
                grid-column: 1;
                grid-row: 1;
            }
            
            .stats-card {
                grid-column: 1;
                grid-row: 2;
            }
            
            .gallery-section {
                grid-column: 1;
                grid-row: 3;
            }
        }

        @media (max-width: 768px) {
            .dashboard-layout {
                padding: 15px;
            }
            
            .dashboard-header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
                padding: 25px 20px;
            }
            
            .dashboard-title {
                font-size: 1.5rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .dashboard-gallery {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                padding: 20px;
            }
        }

        @media (max-width: 480px) {
            .dashboard-gallery {
                grid-template-columns: 1fr;
            }
            
            .dashboard-image {
                height: 150px;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-layout">
        <div class="dashboard-header">
            <h1 class="dashboard-title">天府新区会计师事务所审计项目</h1>
            <div class="header-actions">
                <button>
                    <i class="el-icon-video-play"></i> 观看视频
                </button>
            </div>
        </div>

        <div class="dashboard-grid">
            <!-- 项目信息卡片 -->
            <div class="grid-item info-card">
                <div class="card-header">
                    <i class="el-icon-info"></i>
                    <span>项目信息</span>
                </div>
                <div class="card-content">
                    <p>
                        天府新区会计师事务所审计项目是针对中小企业财务状况进行全面审计的综合性项目。本项目涉及企业财务报表审计、内部控制评价、风险评估等多个方面，旨在为企业提供专业的财务咨询服务，确保财务信息的真实性和准确性。项目组由资深注册会计师领导，配备专业的审计团队，运用先进的审计技术和方法。
                    </p>
                    
                    <div class="progress-section">
                        <div class="progress-item">
                            <div class="progress-label">
                                <span>项目进度</span>
                                <span>85%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 85%;"></div>
                            </div>
                        </div>
                        <div class="progress-item">
                            <div class="progress-label">
                                <span>质量评分</span>
                                <span>92%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 92%;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="grid-item stats-card">
                <div class="card-header">
                    <i class="el-icon-data-analysis"></i>
                    <span>项目统计</span>
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">6</div>
                        <div class="stat-label">项目截图</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">8</div>
                        <div class="stat-label">团队成员</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">30</div>
                        <div class="stat-label">项目天数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">15</div>
                        <div class="stat-label">审计模块</div>
                    </div>
                </div>
            </div>

            <!-- 图片展示区域 -->
            <div class="grid-item gallery-section">
                <div class="section-header">
                    <h3><i class="el-icon-picture-outline"></i> 项目截图展示</h3>
                </div>
                <div class="dashboard-gallery">
                    <div class="dashboard-image" data-title="审计工作底稿"></div>
                    <div class="dashboard-image" data-title="财务报表分析"></div>
                    <div class="dashboard-image" data-title="内控测试记录"></div>
                    <div class="dashboard-image" data-title="风险评估表"></div>
                    <div class="dashboard-image" data-title="审计报告"></div>
                    <div class="dashboard-image" data-title="管理建议书"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加交互效果
        document.querySelector('.header-actions button').addEventListener('click', function() {
            alert('跳转到视频播放页面');
        });

        document.querySelectorAll('.dashboard-image').forEach(item => {
            item.addEventListener('click', function() {
                const title = this.textContent.trim().replace(/\d+/, '').trim();
                alert('查看详情：' + title);
            });
        });

        // 动画效果
        window.addEventListener('load', function() {
            const progressFills = document.querySelectorAll('.progress-fill');
            progressFills.forEach(fill => {
                const width = fill.style.width;
                fill.style.width = '0%';
                setTimeout(() => {
                    fill.style.width = width;
                }, 500);
            });
        });
    </script>
</body>
</html>
