<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>方案一：现代卡片式布局</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .modern-project-intro {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header-card, .description-card, .gallery-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .header-card:hover, .description-card:hover, .gallery-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .header-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 40px;
        }

        .project-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .project-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .video-btn-modern {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 30px;
            font-size: 16px;
            border-radius: 50px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .video-btn-modern:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .card-header {
            display: flex;
            align-items: center;
            padding: 20px 24px;
            border-bottom: 1px solid #f0f0f0;
            background: #fafafa;
        }

        .card-header i {
            font-size: 20px;
            margin-right: 10px;
            color: #667eea;
        }

        .card-header span {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .description-text {
            padding: 24px;
            font-size: 16px;
            line-height: 1.8;
            color: #666;
            text-align: justify;
        }

        .modern-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 24px;
        }

        .gallery-item {
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            cursor: pointer;
        }

        .image-wrapper {
            position: relative;
            overflow: hidden;
            border-radius: 12px;
            background: #f5f5f5;
        }

        .gallery-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(102, 126, 234, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .image-overlay i {
            font-size: 30px;
            color: white;
        }

        .gallery-item:hover .image-overlay {
            opacity: 1;
        }

        .gallery-item:hover .gallery-image {
            transform: scale(1.1);
        }

        .sample-image {
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .modern-project-intro {
                padding: 10px;
            }
            
            .project-title {
                font-size: 2rem;
            }
            
            .modern-gallery {
                grid-template-columns: 1fr;
                padding: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="modern-project-intro">
        <!-- 头部卡片 -->
        <div class="header-card">
            <div class="header-content">
                <h1 class="project-title">天府新区会计师事务所审计项目</h1>
                <p class="project-subtitle">项目及项目组情况</p>
                <button class="video-btn-modern">
                    <i class="el-icon-video-play"></i> 观看视频
                </button>
            </div>
        </div>

        <!-- 描述卡片 -->
        <div class="description-card">
            <div class="card-header">
                <i class="el-icon-document"></i>
                <span>项目描述</span>
            </div>
            <p class="description-text">
                天府新区会计师事务所审计项目是针对中小企业财务状况进行全面审计的综合性项目。本项目涉及企业财务报表审计、内部控制评价、风险评估等多个方面，旨在为企业提供专业的财务咨询服务，确保财务信息的真实性和准确性。项目组由资深注册会计师领导，配备专业的审计团队，运用先进的审计技术和方法，为客户提供高质量的审计服务。
            </p>
        </div>

        <!-- 图片展示卡片 -->
        <div class="gallery-card">
            <div class="card-header">
                <i class="el-icon-picture"></i>
                <span>项目截图</span>
            </div>
            <div class="modern-gallery">
                <div class="gallery-item">
                    <div class="image-wrapper">
                        <div class="gallery-image sample-image">审计工作底稿</div>
                        <div class="image-overlay">
                            <i class="el-icon-zoom-in"></i>
                        </div>
                    </div>
                </div>
                <div class="gallery-item">
                    <div class="image-wrapper">
                        <div class="gallery-image sample-image">财务报表分析</div>
                        <div class="image-overlay">
                            <i class="el-icon-zoom-in"></i>
                        </div>
                    </div>
                </div>
                <div class="gallery-item">
                    <div class="image-wrapper">
                        <div class="gallery-image sample-image">内控测试记录</div>
                        <div class="image-overlay">
                            <i class="el-icon-zoom-in"></i>
                        </div>
                    </div>
                </div>
                <div class="gallery-item">
                    <div class="image-wrapper">
                        <div class="gallery-image sample-image">风险评估表</div>
                        <div class="image-overlay">
                            <i class="el-icon-zoom-in"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加点击效果
        document.querySelectorAll('.gallery-item').forEach(item => {
            item.addEventListener('click', function() {
                alert('点击查看大图：' + this.querySelector('.sample-image').textContent);
            });
        });

        document.querySelector('.video-btn-modern').addEventListener('click', function() {
            alert('跳转到视频播放页面');
        });
    </script>
</body>
</html>
