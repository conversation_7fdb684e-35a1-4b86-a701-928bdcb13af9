<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>方案二：专业分栏布局</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background: #f8f9fa;
            color: #333;
        }

        .professional-layout {
            min-height: 100vh;
            background: linear-gradient(to right, #e3f2fd 0%, #f8f9fa 50%, #fff 100%);
        }

        .layout-container {
            display: flex;
            max-width: 1400px;
            margin: 0 auto;
            min-height: 100vh;
        }

        .info-sidebar {
            width: 400px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 40px 30px;
            position: relative;
        }

        .info-sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            right: -20px;
            width: 40px;
            height: 100%;
            background: linear-gradient(135deg, #2a5298 0%, transparent 100%);
            clip-path: polygon(0 0, 50% 0, 0 100%);
        }

        .project-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .project-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            backdrop-filter: blur(10px);
        }

        .project-icon i {
            font-size: 36px;
            color: white;
        }

        .project-name {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 20px;
            line-height: 1.3;
        }

        .project-tags {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .tag {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .info-section {
            margin-bottom: 40px;
        }

        .info-section h3 {
            font-size: 1.2rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            border-bottom: 2px solid rgba(255, 255, 255, 0.3);
            padding-bottom: 10px;
        }

        .info-section h3 i {
            margin-right: 10px;
            font-size: 18px;
        }

        .description {
            line-height: 1.8;
            font-size: 14px;
            opacity: 0.9;
            text-align: justify;
        }

        .action-btn {
            width: 100%;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .content-main {
            flex: 1;
            padding: 40px;
            background: white;
        }

        .section-header {
            margin-bottom: 30px;
        }

        .section-header h2 {
            font-size: 1.8rem;
            color: #1e3c72;
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .section-header h2 i {
            margin-right: 12px;
            font-size: 24px;
        }

        .section-divider {
            height: 4px;
            background: linear-gradient(to right, #1e3c72, #2a5298, transparent);
            border-radius: 2px;
            width: 100px;
        }

        .professional-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
        }

        .pro-gallery-item {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .pro-gallery-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .image-container {
            position: relative;
            height: 200px;
            background: linear-gradient(45deg, #1e3c72, #2a5298);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .image-info {
            position: absolute;
            top: 15px;
            right: 15px;
        }

        .image-number {
            background: rgba(255, 255, 255, 0.9);
            color: #1e3c72;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 14px;
        }

        @media (max-width: 1024px) {
            .layout-container {
                flex-direction: column;
            }
            
            .info-sidebar {
                width: 100%;
                padding: 30px 20px;
            }
            
            .info-sidebar::before {
                display: none;
            }
            
            .content-main {
                padding: 30px 20px;
            }
        }

        @media (max-width: 768px) {
            .professional-gallery {
                grid-template-columns: 1fr;
            }
            
            .project-name {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="professional-layout">
        <div class="layout-container">
            <!-- 左侧信息栏 -->
            <div class="info-sidebar">
                <div class="project-header">
                    <div class="project-icon">
                        <i class="el-icon-office-building"></i>
                    </div>
                    <h1 class="project-name">天府新区会计师事务所审计项目</h1>
                    <div class="project-tags">
                        <span class="tag">审计项目</span>
                        <span class="tag">进行中</span>
                        <span class="tag">重要</span>
                    </div>
                </div>

                <div class="info-section">
                    <h3><i class="el-icon-document-copy"></i> 项目描述</h3>
                    <p class="description">
                        天府新区会计师事务所审计项目是针对中小企业财务状况进行全面审计的综合性项目。本项目涉及企业财务报表审计、内部控制评价、风险评估等多个方面，旨在为企业提供专业的财务咨询服务，确保财务信息的真实性和准确性。
                    </p>
                </div>

                <div class="info-section">
                    <h3><i class="el-icon-user"></i> 项目团队</h3>
                    <p class="description">
                        项目组由资深注册会计师领导，配备专业的审计团队，运用先进的审计技术和方法，为客户提供高质量的审计服务。
                    </p>
                </div>

                <div class="action-section">
                    <button class="action-btn">
                        <i class="el-icon-video-play"></i> 观看介绍视频
                    </button>
                </div>
            </div>

            <!-- 右侧内容区 -->
            <div class="content-main">
                <div class="section-header">
                    <h2><i class="el-icon-picture-outline"></i> 项目截图展示</h2>
                    <div class="section-divider"></div>
                </div>
                
                <div class="professional-gallery">
                    <div class="pro-gallery-item">
                        <div class="image-container">
                            审计工作底稿
                            <div class="image-info">
                                <span class="image-number">1</span>
                            </div>
                        </div>
                    </div>
                    <div class="pro-gallery-item">
                        <div class="image-container">
                            财务报表分析
                            <div class="image-info">
                                <span class="image-number">2</span>
                            </div>
                        </div>
                    </div>
                    <div class="pro-gallery-item">
                        <div class="image-container">
                            内控测试记录
                            <div class="image-info">
                                <span class="image-number">3</span>
                            </div>
                        </div>
                    </div>
                    <div class="pro-gallery-item">
                        <div class="image-container">
                            风险评估表
                            <div class="image-info">
                                <span class="image-number">4</span>
                            </div>
                        </div>
                    </div>
                    <div class="pro-gallery-item">
                        <div class="image-container">
                            审计报告
                            <div class="image-info">
                                <span class="image-number">5</span>
                            </div>
                        </div>
                    </div>
                    <div class="pro-gallery-item">
                        <div class="image-container">
                            管理建议书
                            <div class="image-info">
                                <span class="image-number">6</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加交互效果
        document.querySelectorAll('.pro-gallery-item').forEach(item => {
            item.addEventListener('click', function() {
                const title = this.querySelector('.image-container').textContent.trim().replace(/\d+/, '').trim();
                alert('查看详情：' + title);
            });
        });

        document.querySelector('.action-btn').addEventListener('click', function() {
            alert('跳转到视频播放页面');
        });
    </script>
</body>
</html>
