<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>方案三：全屏沉浸式布局</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            overflow-x: hidden;
        }

        .immersive-layout {
            width: 100%;
        }

        /* 英雄区域 */
        .hero-section {
            height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .hero-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, 
                rgba(30, 60, 114, 0.9) 0%, 
                rgba(42, 82, 152, 0.8) 50%, 
                rgba(74, 144, 226, 0.7) 100%),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
            background-size: cover;
            background-position: center;
            animation: backgroundMove 20s ease-in-out infinite;
        }

        @keyframes backgroundMove {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.1) rotate(1deg); }
        }

        .hero-content {
            text-align: center;
            color: white;
            z-index: 2;
            max-width: 800px;
            padding: 0 20px;
            animation: fadeInUp 1s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 40px;
            opacity: 0.95;
            line-height: 1.6;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .hero-btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border: none;
            color: white;
            padding: 18px 40px;
            font-size: 18px;
            font-weight: 600;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .hero-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
        }

        .hero-btn i {
            margin-right: 10px;
        }

        /* 内容区域 */
        .content-section {
            background: linear-gradient(to bottom, #f8f9fa, white);
            padding: 80px 0;
            position: relative;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 60px;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border-radius: 2px;
        }

        /* 轮播图样式 */
        .project-carousel {
            margin-bottom: 40px;
        }

        .carousel-item {
            position: relative;
            height: 400px;
            border-radius: 20px;
            overflow: hidden;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .carousel-item:hover {
            transform: scale(1.02);
        }

        .carousel-caption {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
            padding: 40px 30px 30px;
            color: white;
        }

        .carousel-caption h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .carousel-caption p {
            opacity: 0.9;
            font-size: 1rem;
        }

        /* 统计信息 */
        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-top: 60px;
        }

        .stat-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 1rem;
            font-weight: 500;
        }

        .stat-icon {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 15px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.1rem;
            }
            
            .hero-btn {
                padding: 15px 30px;
                font-size: 16px;
            }
            
            .section-title {
                font-size: 2rem;
            }
            
            .carousel-item {
                height: 300px;
                font-size: 18px;
            }
            
            .stats-section {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }
        }

        @media (max-width: 480px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .stats-section {
                grid-template-columns: 1fr;
            }
        }

        /* 滚动指示器 */
        .scroll-indicator {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateX(-50%) translateY(0);
            }
            40% {
                transform: translateX(-50%) translateY(-10px);
            }
            60% {
                transform: translateX(-50%) translateY(-5px);
            }
        }
    </style>
</head>
<body>
    <div class="immersive-layout">
        <!-- 英雄区域 -->
        <div class="hero-section">
            <div class="hero-background"></div>
            <div class="hero-content">
                <h1 class="hero-title">天府新区会计师事务所</h1>
                <p class="hero-subtitle">
                    专业的审计项目管理平台，为中小企业提供全面的财务审计服务，
                    确保财务信息的真实性和准确性，助力企业健康发展
                </p>
                <button class="hero-btn">
                    <i class="el-icon-video-play"></i>
                    观看项目介绍
                </button>
            </div>
            <div class="scroll-indicator">
                <i class="el-icon-arrow-down" style="font-size: 24px;"></i>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-section">
            <div class="container">
                <h2 class="section-title">项目截图展示</h2>
                
                <!-- 模拟轮播图 -->
                <div class="project-carousel">
                    <div class="carousel-container" style="display: flex; gap: 20px; overflow-x: auto; padding: 20px 0;">
                        <div class="carousel-item" style="min-width: 350px;">
                            审计工作底稿
                            <div class="carousel-caption">
                                <h3>审计工作底稿</h3>
                                <p>详细记录审计过程中的各项工作内容和发现</p>
                            </div>
                        </div>
                        <div class="carousel-item" style="min-width: 350px;">
                            财务报表分析
                            <div class="carousel-caption">
                                <h3>财务报表分析</h3>
                                <p>深入分析企业财务状况和经营成果</p>
                            </div>
                        </div>
                        <div class="carousel-item" style="min-width: 350px;">
                            内控测试记录
                            <div class="carousel-caption">
                                <h3>内控测试记录</h3>
                                <p>评估企业内部控制制度的有效性</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="stats-section">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="el-icon-document"></i>
                        </div>
                        <div class="stat-number">6</div>
                        <div class="stat-label">项目截图</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="el-icon-user"></i>
                        </div>
                        <div class="stat-number">8</div>
                        <div class="stat-label">团队成员</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="el-icon-time"></i>
                        </div>
                        <div class="stat-number">30</div>
                        <div class="stat-label">项目天数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="el-icon-success"></i>
                        </div>
                        <div class="stat-number">95%</div>
                        <div class="stat-label">完成度</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加交互效果
        document.querySelector('.hero-btn').addEventListener('click', function() {
            alert('跳转到视频播放页面');
        });

        // 平滑滚动效果
        document.querySelector('.scroll-indicator').addEventListener('click', function() {
            document.querySelector('.content-section').scrollIntoView({
                behavior: 'smooth'
            });
        });

        // 轮播图点击效果
        document.querySelectorAll('.carousel-item').forEach(item => {
            item.addEventListener('click', function() {
                const title = this.querySelector('h3').textContent;
                alert('查看详情：' + title);
            });
        });
    </script>
</body>
</html>
